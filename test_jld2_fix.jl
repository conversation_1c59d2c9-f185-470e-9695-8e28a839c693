using JLD2, FileIO

# 修复JLD2文件的函数 - 使用更安全的方法
function fix_jld2_file_safe(input_filepath, output_filepath)
    println("正在修复文件: $input_filepath")

    # 检查文件是否存在
    if !isfile(input_filepath)
        println("错误: 输入文件不存在")
        return false
    end

    try
        # 尝试使用更安全的读取方法
        data = Dict()

        # 方法1: 尝试直接读取特定键
        jldopen(input_filepath, "r") do file
            println("成功打开文件")

            # 尝试读取 "result" 键，这是我们看到的主要键
            if haskey(file, "result")
                println("尝试读取 result 键...")
                try
                    # 尝试分块读取或使用不同的方法
                    result_data = file["result"]
                    data["result"] = result_data
                    println("成功读取 result 数据")
                catch e
                    println("读取 result 失败，尝试其他方法: $e")

                    # 如果直接读取失败，尝试获取数据类型信息
                    try
                        # 获取组信息
                        if isa(file["result"], JLD2.Group)
                            println("result 是一个组，尝试读取组内容...")
                            group = file["result"]
                            group_data = Dict()
                            for key in keys(group)
                                println("读取组键: $key")
                                group_data[key] = group[key]
                            end
                            data["result"] = group_data
                        else
                            println("result 不是组，跳过...")
                            return false
                        end
                    catch e2
                        println("读取组内容也失败: $e2")
                        return false
                    end
                end
            else
                println("文件中没有 result 键")
                return false
            end
        end

        # 如果成功读取了数据，保存到新文件
        if !isempty(data)
            println("正在保存修复后的文件到: $output_filepath")
            jldsave(output_filepath; data...)
            println("成功保存修复后的文件")

            # 验证新文件
            println("验证新文件...")
            test_data = load(output_filepath)
            println("验证成功! 新文件包含键: ", keys(test_data))

            return true
        else
            println("没有成功读取任何数据")
            return false
        end

    catch e
        println("修复失败: ", e)
        return false
    end
end

# 创建一个简单的替代加载函数
function safe_load_jld2(filepath)
    println("尝试安全加载: $filepath")

    try
        # 首先尝试标准加载
        return load(filepath)
    catch e1
        println("标准加载失败: $e1")

        # 尝试使用jldopen
        try
            data = Dict()
            jldopen(filepath, "r") do file
                # 只读取我们能安全读取的键
                for key in keys(file)
                    try
                        data[key] = file[key]
                    catch e2
                        println("跳过损坏的键 $key: $e2")
                    end
                end
            end
            return data
        catch e2
            println("jldopen也失败: $e2")
            throw(e1)  # 抛出原始错误
        end
    end
end

# 批量修复所有JLD2文件的函数
function fix_all_jld2_files()
    # 从原始脚本中获取文件路径
    disk = "/media/vcl/vcl003/"
    subject = "AF3"
    recordSession = "002"
    testId = ["003","004","005","006"]
    recordPlane = "001"

    siteId = []
    for i = 1:size(testId,1)
        siteid = join(["$(recordSession)_", testId[i], "_$(recordPlane)"])
        push!(siteId, siteid)
    end

    dataExportFolder = joinpath.(disk, subject, "2P_analysis", join(["U",recordSession]), siteId, "DataExport")

    # 为每个文件夹查找并修复JLD2文件
    for i = 1:length(dataExportFolder)
        folder = dataExportFolder[i]
        println("处理文件夹: $folder")

        if !isdir(folder)
            println("警告: 文件夹不存在: $folder")
            continue
        end

        # 查找tuning_result.jld2文件
        files = readdir(folder)
        jld2_files = filter(f -> occursin(r".*_tuning_result\.jld2$", f), files)

        for jld2_file in jld2_files
            input_path = joinpath(folder, jld2_file)
            output_path = joinpath(folder, replace(jld2_file, ".jld2" => "_fixed.jld2"))

            println("修复文件: $input_path")
            success = fix_jld2_file_safe(input_path, output_path)

            if success
                println("成功修复: $jld2_file")
                # 可选：备份原文件并替换
                backup_path = joinpath(folder, replace(jld2_file, ".jld2" => "_backup.jld2"))
                cp(input_path, backup_path)
                cp(output_path, input_path)
                rm(output_path)
                println("已替换原文件并创建备份")
            else
                println("修复失败: $jld2_file")
            end
        end
    end
end

# 执行修复
fix_all_jld2_files()
